// 环境配置
const CONFIG = {
    // 开发环境
    development: {
        API_BASE_URL: 'http://localhost:3000',
        PAYMENT_ENABLED: true
    },
    
    // 生产环境 - Netlify + Vercel 后端
    production: {
        API_BASE_URL: 'https://artisticrealm.vercel.app', // 替换为你的 Vercel 后端URL
        PAYMENT_ENABLED: true
    }
};

// 自动检测环境
function getEnvironment() {
    const hostname = window.location.hostname;
    
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return 'development';
    } else if (hostname.includes('netlify.app') || hostname.includes('your-domain.com')) {
        // Netlify 部署环境，连接 Vercel 后端
        return 'production';
    }
}

// 获取当前配置
const currentConfig = CONFIG[getEnvironment()];

// 导出配置
window.APP_CONFIG = currentConfig;
