{"name": "photography-portfolio-backend", "version": "1.0.0", "description": "艺境光影摄影工作室后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["photography", "portfolio", "alipay", "express"], "author": "艺境光影摄影工作室", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "alipay-sdk": "^3.4.0", "nodemailer": "^6.9.7"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}